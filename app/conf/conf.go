package conf

type Conf struct {
	AppUri       string `yaml:"app_uri"`
	AuthToken    string `yaml:"auth_token"`
	Listen       string `yaml:"listen"`
	OutputLog    string `yaml:"output_log"`
	StaticPath   string `yaml:"static_path"`
	SqlitePath   string `yaml:"sqlite_path"`
	TronGrpcNode string `yaml:"tron_grpc_node"`
	AptosRpcNode string `yaml:"aptos_rpc_node"`
	WebhookUrl   string `yaml:"webhook_url"`
	Pay          struct {
		TrxAtom          float64  `yaml:"trx_atom"`
		TrxRate          string   `yaml:"trx_rate"`
		UsdtAtom         float64  `yaml:"usdt_atom"`
		UsdcAtom         float64  `yaml:"usdc_atom"`
		UsdtRate         string   `yaml:"usdt_rate"`
		UsdcRate         string   `yaml:"usdc_rate"`
		ExpireTime       int      `yaml:"expire_time"`
		WalletAddress    []string `yaml:"wallet_address"`
		TradeIsConfirmed bool     `yaml:"trade_is_confirmed"`
		PaymentAmountMin float64  `yaml:"payment_amount_min"`
		PaymentAmountMax float64  `yaml:"payment_amount_max"`
	} `yaml:"pay"`
	EvmRpc struct {
		Bsc      string `yaml:"bsc"`
		Solana   string `yaml:"solana"`
		Xlayer   string `yaml:"xlayer"`
		Polygon  string `yaml:"polygon"`
		Arbitrum string `yaml:"arbitrum"`
		Ethereum string `yaml:"ethereum"`
		Base     string `yaml:"base"`
	} `yaml:"evm_rpc"`
	Bot struct {
		Token   string `yaml:"token"`
		AdminID int64  `yaml:"admin_id"`
		GroupID string `yaml:"group_id"`
	} `yaml:"bot"`
	Chains struct {
		Tron     bool `yaml:"tron"`
		Bsc      bool `yaml:"bsc"`
		Ethereum bool `yaml:"ethereum"`
		Polygon  bool `yaml:"polygon"`
		Arbitrum bool `yaml:"arbitrum"`
		Xlayer   bool `yaml:"xlayer"`
		Base     bool `yaml:"base"`
		Solana   bool `yaml:"solana"`
		Aptos    bool `yaml:"aptos"`
	} `yaml:"chains"`
}
