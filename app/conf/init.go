package conf

import (
	"errors"
	"flag"
	"fmt"
	"math"
	"os"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gopkg.in/yaml.v3"
)

const (
	Bsc      = "bsc" // Binance Smart Chain
	Tron     = "tron"
	Aptos    = "aptos"
	Solana   = "solana"
	Xlayer   = "xlayer"
	Polygon  = "polygon"
	Arbitrum = "arbitrum"
	Ethereum = "ethereum"
	Base     = "base"
)

var (
	cfg  Conf
	path string
)

func Init() error {
	flag.StringVar(&path, "conf", "./conf.yaml", "config file path")
	flag.Parse()

	data, err := os.ReadFile(path)
	if err != nil {

		return fmt.Errorf("配置文件加载失败：%w", err)
	}

	if err = yaml.Unmarshal(data, &cfg); err != nil {

		return fmt.Errorf("配置数据解析失败：%w", err)
	}

	if BotToken() == "" || BotAdminID() == 0 {

		return errors.New("telegram bot 参数 admin_id 或 token 均不能为空")
	}

	// 验证启用的区块链网络配置
	if err := validateChainConfigs(); err != nil {
		return fmt.Errorf("区块链网络配置验证失败：%w", err)
	}

	return nil
}

func GetUsdtRate() string {

	return cfg.Pay.UsdtRate
}

func GetUsdcRate() string {

	return cfg.Pay.UsdcRate
}

func GetTrxRate() string {

	return cfg.Pay.TrxRate
}

func GetUsdtAtomicity() (decimal.Decimal, int) {
	var val = defaultUsdtAtomicity
	if cfg.Pay.UsdtAtom != 0 {

		val = cfg.Pay.UsdtAtom
	}

	var atom = decimal.NewFromFloat(val)

	return atom, cast.ToInt(math.Abs(float64(atom.Exponent())))
}

func GetUsdcAtomicity() (decimal.Decimal, int) {
	var val = defaultUsdcAtomicity
	if cfg.Pay.UsdcAtom != 0 {

		val = cfg.Pay.UsdcAtom
	}

	var atom = decimal.NewFromFloat(val)

	return atom, cast.ToInt(math.Abs(float64(atom.Exponent())))
}

func GetTrxAtomicity() (decimal.Decimal, int) {
	var val = defaultTrxAtomicity
	if cfg.Pay.TrxAtom != 0 {

		val = cfg.Pay.TrxAtom
	}

	var atom = decimal.NewFromFloat(val)

	return atom, cast.ToInt(math.Abs(float64(atom.Exponent())))
}

func GetExpireTime() time.Duration {
	if cfg.Pay.ExpireTime == 0 {

		return time.Duration(defaultExpireTime)
	}

	return time.Duration(cfg.Pay.ExpireTime)
}

func GetAuthToken() string {
	if cfg.AuthToken == "" {

		return defaultAuthToken
	}

	return cfg.AuthToken
}

func GetAppUri(host string) string {
	if cfg.AppUri != "" {

		return cfg.AppUri
	}

	return host
}

func GetStaticPath() string {

	return cfg.StaticPath
}

func GetSqlitePath() string {
	if cfg.SqlitePath != "" {

		return cfg.SqlitePath
	}

	return defaultSqlitePath
}

func GetOutputLog() string {
	if cfg.OutputLog != "" {
		return cfg.OutputLog
	}

	return defaultOutputLog
}

func GetListen() string {
	if cfg.Listen != "" {
		return cfg.Listen
	}

	return defaultListen
}

func BotToken() string {
	var token = strings.TrimSpace(os.Getenv("BOT_TOKEN"))
	if token != "" {
		return token
	}

	return cfg.Bot.Token
}

func BotAdminID() int64 {
	var id = strings.TrimSpace(os.Getenv("BOT_ADMIN_ID"))
	if id != "" {

		return cast.ToInt64(id)
	}

	return cfg.Bot.AdminID
}

func BotNotifyTarget() string {
	if cfg.Bot.GroupID != "" {

		return cfg.Bot.GroupID
	}

	return cast.ToString(cfg.Bot.AdminID)
}

func GetWalletAddress() []string {

	return cfg.Pay.WalletAddress
}

func GetTradeIsConfirmed() bool {

	return cfg.Pay.TradeIsConfirmed
}

func GetPaymentAmountMin() decimal.Decimal {
	var val = defaultPaymentMinAmount
	if cfg.Pay.PaymentAmountMin != 0 {

		val = cfg.Pay.PaymentAmountMin
	}

	return decimal.NewFromFloat(val)
}

func GetPaymentAmountMax() decimal.Decimal {
	var val float64 = defaultPaymentMaxAmount
	if cfg.Pay.PaymentAmountMax != 0 {

		val = cfg.Pay.PaymentAmountMax
	}

	return decimal.NewFromFloat(val)
}

func GetWebhookUrl() string {

	return cfg.WebhookUrl
}

// 区块链网络启用状态获取函数
func IsChainEnabled(chain string) bool {
	switch chain {
	case Tron:
		return cfg.Chains.Tron
	case Bsc:
		return cfg.Chains.Bsc
	case Ethereum:
		return cfg.Chains.Ethereum
	case Polygon:
		return cfg.Chains.Polygon
	case Arbitrum:
		return cfg.Chains.Arbitrum
	case Xlayer:
		return cfg.Chains.Xlayer
	case Base:
		return cfg.Chains.Base
	case Solana:
		return cfg.Chains.Solana
	case Aptos:
		return cfg.Chains.Aptos
	default:
		return false
	}
}

// validateChainConfigs 验证启用的区块链网络配置
func validateChainConfigs() error {
	// 验证 TRON 网络配置
	if cfg.Chains.Tron && cfg.TronGrpcNode == "" {
		return errors.New("TRON 网络已启用但未配置 tron_grpc_node")
	}

	// 验证 BSC 网络配置
	if cfg.Chains.Bsc && cfg.EvmRpc.Bsc == "" {
		return errors.New("BSC 网络已启用但未配置 evm_rpc.bsc")
	}

	// 验证 Ethereum 网络配置
	if cfg.Chains.Ethereum && cfg.EvmRpc.Ethereum == "" {
		return errors.New("Ethereum 网络已启用但未配置 evm_rpc.ethereum")
	}

	// 验证 Polygon 网络配置
	if cfg.Chains.Polygon && cfg.EvmRpc.Polygon == "" {
		return errors.New("Polygon 网络已启用但未配置 evm_rpc.polygon")
	}

	// 验证 Arbitrum 网络配置
	if cfg.Chains.Arbitrum && cfg.EvmRpc.Arbitrum == "" {
		return errors.New("Arbitrum 网络已启用但未配置 evm_rpc.arbitrum")
	}

	// 验证 Xlayer 网络配置
	if cfg.Chains.Xlayer && cfg.EvmRpc.Xlayer == "" {
		return errors.New("Xlayer 网络已启用但未配置 evm_rpc.xlayer")
	}

	// 验证 Base 网络配置
	if cfg.Chains.Base && cfg.EvmRpc.Base == "" {
		return errors.New("Base 网络已启用但未配置 evm_rpc.base")
	}

	// 验证 Solana 网络配置
	if cfg.Chains.Solana && cfg.EvmRpc.Solana == "" {
		return errors.New("Solana 网络已启用但未配置 evm_rpc.solana")
	}

	// 验证 Aptos 网络配置
	if cfg.Chains.Aptos && cfg.AptosRpcNode == "" {
		return errors.New("Aptos 网络已启用但未配置 aptos_rpc_node")
	}

	return nil
}
