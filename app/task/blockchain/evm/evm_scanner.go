package evm

import (
	"context"
	"fmt"
	"sync"
	"time"

	"bepusdt/app/task/core"
	"bepusdt/app/task/utils"
)

// EVMScanner EVM区块链扫描器
type EVMScanner struct {
	config     core.ChainConfig
	client     core.HTTPClient
	parser     core.TransactionParser
	confirmer  core.TransactionConfirmer
	blockQueue core.Queue[core.BlockRange]
	logger     core.Logger
	eventBus   core.EventBus

	// 状态管理
	lastBlockNum int64
	running      bool
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
}

// NewEVMScanner 创建新的EVM扫描器
func NewEVMScanner(config core.ChainConfig, parser core.TransactionParser, confirmer core.TransactionConfirmer, logger core.Logger, eventBus core.EventBus) *EVMScanner {
	ctx, cancel := context.WithCancel(context.Background())

	return &EVMScanner{
		config:       config,
		client:       utils.NewHTTPClient(config.Timeout),
		parser:       parser,
		confirmer:    confirmer,
		blockQueue:   utils.NewQueue[core.BlockRange](config.QueueSize),
		logger:       logger,
		eventBus:     eventBus,
		lastBlockNum: 0,
		running:      false,
		ctx:          ctx,
		cancel:       cancel,
	}
}

// Start 启动扫描器
func (s *EVMScanner) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return core.NewTaskError("SCANNER_ALREADY_RUNNING", "scanner is already running", nil)
	}

	s.logger.Info("Starting EVM scanner", "chain", s.config.Name)

	// 初始化起始区块
	if err := s.initializeStartBlock(); err != nil {
		return core.WrapError("INIT_BLOCK_FAILED", "failed to initialize start block", err)
	}

	// 启动区块滚动工作器
	s.wg.Add(1)
	go s.blockRollWorker()

	// 启动区块扫描工作器
	s.wg.Add(1)
	go s.blockScanWorker()

	// 启动交易确认工作器
	s.wg.Add(1)
	go s.tradeConfirmWorker()

	s.running = true
	s.logger.Info("EVM scanner started", "chain", s.config.Name)

	// 发布启动事件
	event := core.Event{
		Type:   "scanner.started",
		Source: s.config.Name,
		Data: map[string]interface{}{
			"chain": s.config.Name,
			"type":  "evm",
		},
		Timestamp: time.Now(),
	}
	s.eventBus.Publish(event)

	return nil
}

// Stop 停止扫描器
func (s *EVMScanner) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	s.logger.Info("Stopping EVM scanner", "chain", s.config.Name)

	// 取消上下文
	s.cancel()

	// 关闭队列
	s.blockQueue.Close()

	// 等待所有工作器完成
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("EVM scanner stopped gracefully", "chain", s.config.Name)
	case <-time.After(30 * time.Second):
		s.logger.Warn("Scanner stop timeout", "chain", s.config.Name)
	}

	s.running = false

	// 发布停止事件
	event := core.Event{
		Type:   "scanner.stopped",
		Source: s.config.Name,
		Data: map[string]interface{}{
			"chain": s.config.Name,
		},
		Timestamp: time.Now(),
	}
	s.eventBus.Publish(event)

	return nil
}

// GetLatestBlock 获取最新区块高度
func (s *EVMScanner) GetLatestBlock() (int64, error) {
	url := s.config.Endpoint
	requestBody := `{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`

	resp, err := s.client.Post(url, []byte(requestBody))
	if err != nil {
		return 0, core.WrapError("GET_LATEST_BLOCK_FAILED", "failed to get latest block", err)
	}

	if resp.StatusCode != 200 {
		return 0, core.NewTaskError("INVALID_RESPONSE", fmt.Sprintf("invalid response status: %d", resp.StatusCode), nil)
	}

	// 解析响应获取区块高度
	blockNum, err := s.parseBlockNumber(resp.Body)
	if err != nil {
		return 0, core.WrapError("PARSE_BLOCK_NUMBER_FAILED", "failed to parse block number", err)
	}

	return blockNum, nil
}

// ScanBlocks 扫描区块范围
func (s *EVMScanner) ScanBlocks(from, to int64) error {
	if from > to {
		return core.NewTaskError("INVALID_BLOCK_RANGE", "from block cannot be greater than to block", nil)
	}

	blockRange := core.BlockRange{From: from, To: to}

	if err := s.blockQueue.Push(blockRange); err != nil {
		return core.WrapError("QUEUE_PUSH_FAILED", "failed to push block range to queue", err)
	}

	s.logger.Debug("Block range queued for scanning", "from", from, "to", to, "chain", s.config.Name)
	return nil
}

// GetChainName 获取链名称
func (s *EVMScanner) GetChainName() string {
	return s.config.Name
}

// initializeStartBlock 初始化起始区块
func (s *EVMScanner) initializeStartBlock() error {
	latestBlock, err := s.GetLatestBlock()
	if err != nil {
		return err
	}

	s.lastBlockNum = latestBlock + s.config.StartOffset
	if s.lastBlockNum < 0 {
		s.lastBlockNum = 0
	}

	s.logger.Info("Initialized start block", "chain", s.config.Name, "start_block", s.lastBlockNum, "latest_block", latestBlock)
	return nil
}

// blockRollWorker 区块滚动工作器
func (s *EVMScanner) blockRollWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.ScanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if err := s.rollBlocks(); err != nil {
				s.logger.Error("Block roll failed", "chain", s.config.Name, "error", err)
			}
		}
	}
}

// blockScanWorker 区块扫描工作器
func (s *EVMScanner) blockScanWorker() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			blockRange, err := s.blockQueue.Pop()
			if err != nil {
				if err.Error() == "queue is closed" {
					return
				}
				time.Sleep(100 * time.Millisecond)
				continue
			}

			if err := s.scanBlockRange(blockRange); err != nil {
				s.logger.Error("Block scan failed", "chain", s.config.Name, "range", blockRange, "error", err)
				// 重新入队进行重试
				s.blockQueue.Push(blockRange)
			}
		}
	}
}

// tradeConfirmWorker 交易确认工作器
func (s *EVMScanner) tradeConfirmWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if err := s.processTradeConfirmations(); err != nil {
				s.logger.Error("Trade confirmation failed", "chain", s.config.Name, "error", err)
			}
		}
	}
}

// rollBlocks 滚动区块
func (s *EVMScanner) rollBlocks() error {
	latestBlock, err := s.GetLatestBlock()
	if err != nil {
		return err
	}

	// 应用确认偏移量
	confirmedBlock := latestBlock - s.config.ConfirmOffset
	if confirmedBlock <= s.lastBlockNum {
		return nil // 没有新区块需要处理
	}

	// 应用延迟偏移量
	targetBlock := confirmedBlock - s.config.RollDelay

	// 分批处理区块
	for from := s.lastBlockNum + 1; from <= targetBlock; from += int64(s.config.BatchSize) {
		to := from + int64(s.config.BatchSize) - 1
		if to > targetBlock {
			to = targetBlock
		}

		if err := s.ScanBlocks(from, to); err != nil {
			return err
		}
	}

	s.lastBlockNum = targetBlock
	return nil
}

// scanBlockRange 扫描区块范围
func (s *EVMScanner) scanBlockRange(blockRange core.BlockRange) error {
	s.logger.Debug("Scanning block range", "chain", s.config.Name, "from", blockRange.From, "to", blockRange.To)

	// 构建批量请求
	requestBody, err := s.buildBatchRequest(blockRange.From, blockRange.To)
	if err != nil {
		return core.WrapError("BUILD_REQUEST_FAILED", "failed to build batch request", err)
	}

	// 发送请求
	resp, err := s.client.Post(s.config.Endpoint, requestBody)
	if err != nil {
		return core.WrapError("SCAN_REQUEST_FAILED", "failed to send scan request", err)
	}

	if resp.StatusCode != 200 {
		return core.NewTaskError("INVALID_RESPONSE", fmt.Sprintf("invalid response status: %d", resp.StatusCode), nil)
	}

	// 解析交易
	transactions, err := s.parser.ParseTransactions(resp.Body)
	if err != nil {
		return core.WrapError("PARSE_TRANSACTIONS_FAILED", "failed to parse transactions", err)
	}

	// 处理交易
	if len(transactions) > 0 {
		s.logger.Info("Found transactions", "chain", s.config.Name, "count", len(transactions), "range", blockRange)

		// 发布交易事件
		event := core.Event{
			Type:   "transactions.found",
			Source: s.config.Name,
			Data: map[string]interface{}{
				"chain":        s.config.Name,
				"transactions": transactions,
				"block_range":  blockRange,
			},
			Timestamp: time.Now(),
		}
		s.eventBus.Publish(event)
	}

	return nil
}

// processTradeConfirmations 处理交易确认
func (s *EVMScanner) processTradeConfirmations() error {
	// 这里应该从数据库获取待确认的交易
	// 然后使用 confirmer 检查确认状态
	// 这是一个简化的实现
	s.logger.Debug("Processing trade confirmations", "chain", s.config.Name)
	return nil
}

// parseBlockNumber 解析区块号
func (s *EVMScanner) parseBlockNumber(data []byte) (int64, error) {
	// 这里应该解析JSON响应获取区块号
	// 简化实现，实际应该使用JSON解析
	return 0, nil
}

// buildBatchRequest 构建批量请求
func (s *EVMScanner) buildBatchRequest(from, to int64) ([]byte, error) {
	// 这里应该构建批量的eth_getBlockByNumber请求
	// 简化实现
	return []byte{}, nil
}
