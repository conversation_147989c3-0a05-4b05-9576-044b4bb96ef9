package config

import (
	"encoding/json"
	"fmt"
	"time"

	"bepusdt/app/task/core"
)

// ChainConfigManager 链配置管理器
type ChainConfigManager struct {
	configs map[string]core.ChainConfig
}

// NewChainConfigManager 创建新的链配置管理器
func NewChainConfigManager() *ChainConfigManager {
	return &ChainConfigManager{
		configs: make(map[string]core.ChainConfig),
	}
}

// LoadDefaultConfigs 加载默认配置
func (ccm *ChainConfigManager) LoadDefaultConfigs() error {
	// Ethereum配置
	ccm.configs["ethereum"] = core.ChainConfig{
		Name:          "ethereum",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  12 * time.Second,
		ConfirmOffset: 12,
		StartOffset:   -100,
		RollDelay:     0,
		BatchSize:     10,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// BSC配置
	ccm.configs["bsc"] = core.ChainConfig{
		Name:          "bsc",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 15,
		StartOffset:   -400,
		RollDelay:     0,
		BatchSize:     20,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// Arbitrum配置
	ccm.configs["arbitrum"] = core.ChainConfig{
		Name:          "arbitrum",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 40,
		StartOffset:   -600,
		RollDelay:     0,
		BatchSize:     15,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// Polygon配置
	ccm.configs["polygon"] = core.ChainConfig{
		Name:          "polygon",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 40,
		StartOffset:   -600,
		RollDelay:     0,
		BatchSize:     15,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// Base配置
	ccm.configs["base"] = core.ChainConfig{
		Name:          "base",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 40,
		StartOffset:   -600,
		RollDelay:     0,
		BatchSize:     15,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// XLayer配置
	ccm.configs["xlayer"] = core.ChainConfig{
		Name:          "xlayer",
		Type:          "evm",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 40,
		StartOffset:   -600,
		RollDelay:     10, // XLayer需要延迟
		BatchSize:     15,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// TRON配置
	ccm.configs["tron"] = core.ChainConfig{
		Name:          "tron",
		Type:          "tron",
		Enabled:       true,
		ScanInterval:  3 * time.Second,
		ConfirmOffset: 30,
		StartOffset:   -400,
		RollDelay:     0,
		BatchSize:     1, // TRON一次处理一个区块
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// Solana配置
	ccm.configs["solana"] = core.ChainConfig{
		Name:          "solana",
		Type:          "solana",
		Enabled:       true,
		ScanInterval:  5 * time.Second,
		ConfirmOffset: 60,
		StartOffset:   -600,
		RollDelay:     0,
		BatchSize:     100,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	// Aptos配置
	ccm.configs["aptos"] = core.ChainConfig{
		Name:          "aptos",
		Type:          "aptos",
		Enabled:       true,
		ScanInterval:  3 * time.Second,
		ConfirmOffset: 1000,
		StartOffset:   -50000,
		RollDelay:     0,
		BatchSize:     100,
		MaxRetries:    3,
		Timeout:       30 * time.Second,
		QueueSize:     100,
	}

	return nil
}

// GetChainConfig 获取链配置
func (ccm *ChainConfigManager) GetChainConfig(chainName string) (core.ChainConfig, error) {
	config, exists := ccm.configs[chainName]
	if !exists {
		return core.ChainConfig{}, core.NewTaskError("CONFIG_NOT_FOUND", fmt.Sprintf("config for chain %s not found", chainName), nil)
	}
	return config, nil
}

// SetChainConfig 设置链配置
func (ccm *ChainConfigManager) SetChainConfig(chainName string, config core.ChainConfig) {
	ccm.configs[chainName] = config
}

// IsChainEnabled 检查链是否启用
func (ccm *ChainConfigManager) IsChainEnabled(chainName string) bool {
	config, exists := ccm.configs[chainName]
	if !exists {
		return false
	}
	return config.Enabled
}

// GetAllChainConfigs 获取所有链配置
func (ccm *ChainConfigManager) GetAllChainConfigs() map[string]core.ChainConfig {
	result := make(map[string]core.ChainConfig)
	for name, config := range ccm.configs {
		result[name] = config
	}
	return result
}

// GetEnabledChains 获取启用的链
func (ccm *ChainConfigManager) GetEnabledChains() []string {
	var enabled []string
	for name, config := range ccm.configs {
		if config.Enabled {
			enabled = append(enabled, name)
		}
	}
	return enabled
}

// UpdateChainConfig 更新链配置
func (ccm *ChainConfigManager) UpdateChainConfig(chainName string, updates map[string]interface{}) error {
	config, exists := ccm.configs[chainName]
	if !exists {
		return core.NewTaskError("CONFIG_NOT_FOUND", fmt.Sprintf("config for chain %s not found", chainName), nil)
	}

	// 使用反射或手动更新配置字段
	if enabled, ok := updates["enabled"]; ok {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if scanInterval, ok := updates["scan_interval"]; ok {
		if intervalStr, ok := scanInterval.(string); ok {
			if duration, err := time.ParseDuration(intervalStr); err == nil {
				config.ScanInterval = duration
			}
		}
	}

	if confirmOffset, ok := updates["confirm_offset"]; ok {
		if offsetInt, ok := confirmOffset.(int64); ok {
			config.ConfirmOffset = offsetInt
		}
	}

	ccm.configs[chainName] = config
	return nil
}

// LoadFromJSON 从JSON加载配置
func (ccm *ChainConfigManager) LoadFromJSON(data []byte) error {
	var configs map[string]core.ChainConfig
	if err := json.Unmarshal(data, &configs); err != nil {
		return core.WrapError("JSON_PARSE_FAILED", "failed to parse JSON config", err)
	}

	ccm.configs = configs
	return nil
}

// SaveToJSON 保存配置到JSON
func (ccm *ChainConfigManager) SaveToJSON() ([]byte, error) {
	data, err := json.MarshalIndent(ccm.configs, "", "  ")
	if err != nil {
		return nil, core.WrapError("JSON_MARSHAL_FAILED", "failed to marshal config to JSON", err)
	}
	return data, nil
}

// ValidateConfig 验证配置
func (ccm *ChainConfigManager) ValidateConfig(config core.ChainConfig) error {
	if config.Name == "" {
		return core.NewTaskError("INVALID_CONFIG", "chain name cannot be empty", nil)
	}

	if config.Type == "" {
		return core.NewTaskError("INVALID_CONFIG", "chain type cannot be empty", nil)
	}

	if config.ScanInterval <= 0 {
		return core.NewTaskError("INVALID_CONFIG", "scan interval must be positive", nil)
	}

	if config.BatchSize <= 0 {
		return core.NewTaskError("INVALID_CONFIG", "batch size must be positive", nil)
	}

	if config.QueueSize <= 0 {
		return core.NewTaskError("INVALID_CONFIG", "queue size must be positive", nil)
	}

	if config.Timeout <= 0 {
		return core.NewTaskError("INVALID_CONFIG", "timeout must be positive", nil)
	}

	return nil
}
