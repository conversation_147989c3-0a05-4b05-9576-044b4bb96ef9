package core

import (
	"fmt"
	"time"
)

// TaskError 任务错误类型
type TaskError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Cause   error  `json:"cause,omitempty"`
}

func (e *TaskError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *TaskError) Unwrap() error {
	return e.Cause
}

// NewTaskError 创建新的任务错误
func NewTaskError(code, message string, cause error) *TaskError {
	return &TaskError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// 预定义错误类型
var (
	// 配置相关错误
	ErrInvalidConfig    = &TaskError{Code: "INVALID_CONFIG", Message: "invalid configuration"}
	ErrConfigNotFound   = &TaskError{Code: "CONFIG_NOT_FOUND", Message: "configuration not found"}
	ErrConfigLoadFailed = &TaskError{Code: "CONFIG_LOAD_FAILED", Message: "failed to load configuration"}

	// 区块链相关错误
	ErrChainNotSupported = &TaskError{Code: "CHAIN_NOT_SUPPORTED", Message: "blockchain not supported"}
	ErrChainNotEnabled   = &TaskError{Code: "CHAIN_NOT_ENABLED", Message: "blockchain not enabled"}
	ErrScannerFailed     = &TaskError{Code: "SCANNER_FAILED", Message: "blockchain scanner failed"}
	ErrParserFailed      = &TaskError{Code: "PARSER_FAILED", Message: "transaction parser failed"}
	ErrConfirmerFailed   = &TaskError{Code: "CONFIRMER_FAILED", Message: "transaction confirmer failed"}
	ErrBlockNotFound     = &TaskError{Code: "BLOCK_NOT_FOUND", Message: "block not found"}
	ErrTransactionFailed = &TaskError{Code: "TRANSACTION_FAILED", Message: "transaction processing failed"}

	// 任务相关错误
	ErrTaskNotFound      = &TaskError{Code: "TASK_NOT_FOUND", Message: "task not found"}
	ErrTaskAlreadyExists = &TaskError{Code: "TASK_ALREADY_EXISTS", Message: "task already exists"}
	ErrTaskStartFailed   = &TaskError{Code: "TASK_START_FAILED", Message: "failed to start task"}
	ErrTaskStopFailed    = &TaskError{Code: "TASK_STOP_FAILED", Message: "failed to stop task"}
	ErrTaskTimeout       = &TaskError{Code: "TASK_TIMEOUT", Message: "task execution timeout"}

	// 服务相关错误
	ErrServiceNotFound    = &TaskError{Code: "SERVICE_NOT_FOUND", Message: "service not found"}
	ErrServiceStartFailed = &TaskError{Code: "SERVICE_START_FAILED", Message: "failed to start service"}
	ErrServiceStopFailed  = &TaskError{Code: "SERVICE_STOP_FAILED", Message: "failed to stop service"}
	ErrTransferFailed     = &TaskError{Code: "TRANSFER_FAILED", Message: "transfer processing failed"}
	ErrNotificationFailed = &TaskError{Code: "NOTIFICATION_FAILED", Message: "notification failed"}
	ErrWebhookFailed      = &TaskError{Code: "WEBHOOK_FAILED", Message: "webhook request failed"}
	ErrBotMessageFailed   = &TaskError{Code: "BOT_MESSAGE_FAILED", Message: "bot message failed"}

	// 汇率相关错误
	ErrRateNotFound       = &TaskError{Code: "RATE_NOT_FOUND", Message: "exchange rate not found"}
	ErrRateUpdateFailed   = &TaskError{Code: "RATE_UPDATE_FAILED", Message: "failed to update exchange rate"}
	ErrRateProviderFailed = &TaskError{Code: "RATE_PROVIDER_FAILED", Message: "rate provider failed"}
	ErrInvalidAmount      = &TaskError{Code: "INVALID_AMOUNT", Message: "invalid amount"}

	// 网络相关错误
	ErrNetworkTimeout      = &TaskError{Code: "NETWORK_TIMEOUT", Message: "network request timeout"}
	ErrNetworkFailed       = &TaskError{Code: "NETWORK_FAILED", Message: "network request failed"}
	ErrInvalidResponse     = &TaskError{Code: "INVALID_RESPONSE", Message: "invalid response"}
	ErrResponseParseFailed = &TaskError{Code: "RESPONSE_PARSE_FAILED", Message: "failed to parse response"}

	// 数据相关错误
	ErrDataNotFound   = &TaskError{Code: "DATA_NOT_FOUND", Message: "data not found"}
	ErrDataInvalid    = &TaskError{Code: "DATA_INVALID", Message: "invalid data"}
	ErrDataSaveFailed = &TaskError{Code: "DATA_SAVE_FAILED", Message: "failed to save data"}
	ErrDataLoadFailed = &TaskError{Code: "DATA_LOAD_FAILED", Message: "failed to load data"}

	// 队列相关错误
	ErrQueueFull   = &TaskError{Code: "QUEUE_FULL", Message: "queue is full"}
	ErrQueueEmpty  = &TaskError{Code: "QUEUE_EMPTY", Message: "queue is empty"}
	ErrQueueClosed = &TaskError{Code: "QUEUE_CLOSED", Message: "queue is closed"}

	// 系统相关错误
	ErrSystemShutdown    = &TaskError{Code: "SYSTEM_SHUTDOWN", Message: "system is shutting down"}
	ErrResourceExhausted = &TaskError{Code: "RESOURCE_EXHAUSTED", Message: "system resources exhausted"}
	ErrInternalError     = &TaskError{Code: "INTERNAL_ERROR", Message: "internal system error"}
)

// IsTaskError 检查是否为任务错误
func IsTaskError(err error) bool {
	_, ok := err.(*TaskError)
	return ok
}

// GetTaskErrorCode 获取任务错误代码
func GetTaskErrorCode(err error) string {
	if taskErr, ok := err.(*TaskError); ok {
		return taskErr.Code
	}
	return "UNKNOWN_ERROR"
}

// WrapError 包装错误为任务错误
func WrapError(code, message string, cause error) *TaskError {
	return &TaskError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// ErrorHandler 错误处理器
type ErrorHandler interface {
	HandleError(err error) error
	ShouldRetry(err error) bool
	GetRetryDelay(err error, attempt int) time.Duration
}

// DefaultErrorHandler 默认错误处理器
type DefaultErrorHandler struct{}

func (h *DefaultErrorHandler) HandleError(err error) error {
	// 记录错误日志
	// 这里可以添加具体的错误处理逻辑
	return err
}

func (h *DefaultErrorHandler) ShouldRetry(err error) bool {
	if taskErr, ok := err.(*TaskError); ok {
		switch taskErr.Code {
		case "NETWORK_TIMEOUT", "NETWORK_FAILED", "RATE_PROVIDER_FAILED":
			return true
		default:
			return false
		}
	}
	return false
}

func (h *DefaultErrorHandler) GetRetryDelay(err error, attempt int) time.Duration {
	// 指数退避策略
	baseDelay := time.Second
	maxDelay := time.Minute * 5

	delay := baseDelay * time.Duration(1<<uint(attempt))
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}
