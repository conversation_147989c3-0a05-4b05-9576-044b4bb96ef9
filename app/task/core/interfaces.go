package core

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

// BlockchainScanner 区块链扫描器接口
type BlockchainScanner interface {
	Start(ctx context.Context) error
	Stop() error
	GetLatestBlock() (int64, error)
	ScanBlocks(from, to int64) error
	GetChainName() string
}

// TransactionParser 交易解析器接口
type TransactionParser interface {
	ParseTransactions(blockData []byte) ([]Transaction, error)
	ValidateTransaction(tx Transaction) error
	GetSupportedTokens() []string
}

// TransactionConfirmer 交易确认器接口
type TransactionConfirmer interface {
	ConfirmTransaction(txHash string) (bool, error)
	GetConfirmationCount(txHash string) (int64, error)
	IsConfirmed(txHash string, requiredConfirmations int64) (bool, error)
}

// ChainProvider 链提供者接口
type ChainProvider interface {
	GetChainName() string
	GetChainType() string
	GetScanner() BlockchainScanner
	GetParser() TransactionParser
	GetConfirmer() TransactionConfirmer
	Initialize(config ChainConfig) error
	IsEnabled() bool
}

// TaskManager 任务管理器接口
type TaskManager interface {
	RegisterTask(task Task) error
	RegisterChain(chain ChainProvider) error
	StartAll(ctx context.Context) error
	StopAll() error
	GetTaskStatus(taskID string) TaskStatus
	GetChainStatus(chainName string) ChainStatus
}

// TransferService 转账服务接口
type TransferService interface {
	ProcessTransfer(transfer Transfer) error
	ValidateAmount(amount decimal.Decimal) bool
	HandleOrderTransfer(transfer Transfer) error
	HandleNonOrderTransfer(transfer Transfer) error
}

// NotificationService 通知服务接口
type NotificationService interface {
	SendNotification(notification Notification) error
	HandleWebhook(webhook Webhook) error
	SendBotMessage(message BotMessage) error
	RetryFailedNotifications() error
}

// RateService 汇率服务接口
type RateService interface {
	GetRate(currency string) (float64, error)
	UpdateRate(currency string, rate float64) error
	CalculateAmount(amount decimal.Decimal, currency string) (decimal.Decimal, error)
	GetCalculatedRate(currency string) float64
}

// RateProvider 汇率提供者接口
type RateProvider interface {
	GetUSDTRate(ctx context.Context) (float64, error)
	GetUSDCRate(ctx context.Context) (float64, error)
	GetTRXRate(ctx context.Context) (float64, error)
	GetProviderName() string
}

// EventBus 事件总线接口
type EventBus interface {
	Subscribe(eventType string, handler EventHandler) error
	Unsubscribe(eventType string, handler EventHandler) error
	Publish(event Event) error
}

// EventHandler 事件处理器接口
type EventHandler interface {
	Handle(event Event) error
	GetHandlerName() string
}

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
}

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	Get(url string) (*HTTPResponse, error)
	Post(url string, data []byte) (*HTTPResponse, error)
	SetTimeout(timeout time.Duration)
	SetHeaders(headers map[string]string)
}

// Queue 队列接口
type Queue[T any] interface {
	Push(item T) error
	Pop() (T, error)
	Size() int
	IsEmpty() bool
	Close() error
}

// ChainFactory 链工厂接口
type ChainFactory interface {
	CreateChain(chainType string, config ChainConfig) (ChainProvider, error)
	GetSupportedChains() []string
}

// ConfigManager 配置管理器接口
type ConfigManager interface {
	GetChainConfig(chainName string) (ChainConfig, error)
	GetTaskConfig() TaskConfig
	IsChainEnabled(chainName string) bool
	UpdateChainConfig(chainName string, config ChainConfig) error
}

// HealthChecker 健康检查接口
type HealthChecker interface {
	CheckHealth() HealthStatus
	GetHealthReport() HealthReport
}

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	IncrementCounter(name string, labels map[string]string)
	RecordGauge(name string, value float64, labels map[string]string)
	RecordHistogram(name string, value float64, labels map[string]string)
}
