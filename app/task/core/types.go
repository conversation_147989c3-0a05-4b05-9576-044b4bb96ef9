package core

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

// ChainConfig 区块链配置
type ChainConfig struct {
	Name          string        `json:"name"`
	Type          string        `json:"type"` // evm, tron, solana, aptos
	Enabled       bool          `json:"enabled"`
	Endpoint      string        `json:"endpoint"`
	ScanInterval  time.Duration `json:"scan_interval"`
	ConfirmOffset int64         `json:"confirm_offset"`
	StartOffset   int64         `json:"start_offset"`
	RollDelay     int64         `json:"roll_delay"`
	BatchSize     int           `json:"batch_size"`
	MaxRetries    int           `json:"max_retries"`
	Timeout       time.Duration `json:"timeout"`
	QueueSize     int           `json:"queue_size"`
}

// TaskConfig 任务配置
type TaskConfig struct {
	MaxWorkers      int           `json:"max_workers"`
	QueueSize       int           `json:"queue_size"`
	RetryInterval   time.Duration `json:"retry_interval"`
	HealthCheck     time.Duration `json:"health_check"`
	ShutdownTimeout time.Duration `json:"shutdown_timeout"`
}

// Task 任务定义
type Task struct {
	ID       string        `json:"id"`
	Name     string        `json:"name"`
	Duration time.Duration `json:"duration"`
	Callback func(ctx context.Context) error
	Enabled  bool `json:"enabled"`
	Priority int  `json:"priority"`
}

// TaskStatus 任务状态
type TaskStatus struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	Status     string    `json:"status"` // running, stopped, error
	LastRun    time.Time `json:"last_run"`
	NextRun    time.Time `json:"next_run"`
	RunCount   int64     `json:"run_count"`
	ErrorCount int64     `json:"error_count"`
	LastError  string    `json:"last_error,omitempty"`
}

// ChainStatus 链状态
type ChainStatus struct {
	Name           string    `json:"name"`
	Type           string    `json:"type"`
	Status         string    `json:"status"` // active, inactive, error
	LatestBlock    int64     `json:"latest_block"`
	ProcessedBlock int64     `json:"processed_block"`
	LastScan       time.Time `json:"last_scan"`
	ScanCount      int64     `json:"scan_count"`
	ErrorCount     int64     `json:"error_count"`
	LastError      string    `json:"last_error,omitempty"`
}

// Transaction 交易信息
type Transaction struct {
	Network     string          `json:"network"`
	TxHash      string          `json:"tx_hash"`
	Amount      decimal.Decimal `json:"amount"`
	FromAddress string          `json:"from_address"`
	ToAddress   string          `json:"to_address"`
	Timestamp   time.Time       `json:"timestamp"`
	TradeType   string          `json:"trade_type"`
	BlockNum    int64           `json:"block_num"`
	TokenSymbol string          `json:"token_symbol"`
	Decimals    int32           `json:"decimals"`
}

// Transfer 转账信息
type Transfer struct {
	Network     string          `json:"network"`
	TxHash      string          `json:"tx_hash"`
	Amount      decimal.Decimal `json:"amount"`
	FromAddress string          `json:"from_address"`
	RecvAddress string          `json:"recv_address"`
	Timestamp   time.Time       `json:"timestamp"`
	TradeType   string          `json:"trade_type"`
	BlockNum    int64           `json:"block_num"`
}

// Notification 通知信息
type Notification struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // webhook, bot, email
	Target    string                 `json:"target"`
	Subject   string                 `json:"subject"`
	Content   string                 `json:"content"`
	Data      map[string]interface{} `json:"data"`
	Priority  int                    `json:"priority"`
	CreatedAt time.Time              `json:"created_at"`
}

// Webhook Webhook信息
type Webhook struct {
	ID        int64                  `json:"id"`
	Status    int8                   `json:"status"`
	Num       int                    `json:"num"`
	Url       string                 `json:"url"`
	Event     string                 `json:"event"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// BotMessage Bot消息
type BotMessage struct {
	ChatID  int64  `json:"chat_id"`
	Text    string `json:"text"`
	Type    string `json:"type"` // text, markdown, html
	ReplyTo int    `json:"reply_to,omitempty"`
}

// Event 事件信息
type Event struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// HTTPResponse HTTP响应
type HTTPResponse struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       []byte            `json:"body"`
}

// BlockRange 区块范围
type BlockRange struct {
	From int64 `json:"from"`
	To   int64 `json:"to"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string    `json:"status"` // healthy, unhealthy, degraded
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message,omitempty"`
}

// HealthReport 健康报告
type HealthReport struct {
	Overall HealthStatus            `json:"overall"`
	Chains  map[string]HealthStatus `json:"chains"`
	Tasks   map[string]HealthStatus `json:"tasks"`
}

// RateInfo 汇率信息
type RateInfo struct {
	Currency  string    `json:"currency"`
	RawRate   float64   `json:"raw_rate"`
	CalcRate  float64   `json:"calc_rate"`
	UpdatedAt time.Time `json:"updated_at"`
	Provider  string    `json:"provider"`
}
