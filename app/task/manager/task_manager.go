package manager

import (
	"context"
	"fmt"
	"sync"
	"time"

	"bepusdt/app/task/core"
)

// TaskManager 任务管理器实现
type TaskManager struct {
	tasks     map[string]core.Task
	chains    map[string]core.ChainProvider
	services  map[string]interface{}
	container *Container
	scheduler *Scheduler
	eventBus  core.EventBus
	logger    core.Logger
	config    core.TaskConfig
	mu        sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	running   bool
}

// NewTaskManager 创建新的任务管理器
func NewTaskManager(container *Container, eventBus core.EventBus, logger core.Logger, config core.TaskConfig) *TaskManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &TaskManager{
		tasks:     make(map[string]core.Task),
		chains:    make(map[string]core.ChainProvider),
		services:  make(map[string]interface{}),
		container: container,
		scheduler: NewScheduler(logger),
		eventBus:  eventBus,
		logger:    logger,
		config:    config,
		ctx:       ctx,
		cancel:    cancel,
		running:   false,
	}
}

// RegisterTask 注册任务
func (tm *TaskManager) RegisterTask(task core.Task) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if task.Callback == nil {
		return core.NewTaskError("INVALID_TASK", "task callback cannot be nil", nil)
	}

	if _, exists := tm.tasks[task.ID]; exists {
		return core.NewTaskError("TASK_ALREADY_EXISTS", fmt.Sprintf("task %s already exists", task.ID), nil)
	}

	tm.tasks[task.ID] = task
	tm.logger.Info("Task registered", "task_id", task.ID, "task_name", task.Name)

	// 发布任务注册事件
	event := core.Event{
		Type:   "task.registered",
		Source: "task_manager",
		Data: map[string]interface{}{
			"task_id":   task.ID,
			"task_name": task.Name,
		},
		Timestamp: time.Now(),
	}
	tm.eventBus.Publish(event)

	return nil
}

// RegisterChain 注册区块链
func (tm *TaskManager) RegisterChain(chain core.ChainProvider) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	chainName := chain.GetChainName()
	if _, exists := tm.chains[chainName]; exists {
		return core.NewTaskError("CHAIN_ALREADY_EXISTS", fmt.Sprintf("chain %s already exists", chainName), nil)
	}

	if !chain.IsEnabled() {
		tm.logger.Info("Chain is disabled, skipping registration", "chain", chainName)
		return nil
	}

	tm.chains[chainName] = chain
	tm.logger.Info("Chain registered", "chain", chainName, "type", chain.GetChainType())

	// 发布链注册事件
	event := core.Event{
		Type:   "chain.registered",
		Source: "task_manager",
		Data: map[string]interface{}{
			"chain_name": chainName,
			"chain_type": chain.GetChainType(),
		},
		Timestamp: time.Now(),
	}
	tm.eventBus.Publish(event)

	return nil
}

// StartAll 启动所有任务
func (tm *TaskManager) StartAll(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.running {
		return core.NewTaskError("ALREADY_RUNNING", "task manager is already running", nil)
	}

	tm.logger.Info("Starting task manager", "task_count", len(tm.tasks), "chain_count", len(tm.chains))

	// 初始化区块链
	for chainName, chain := range tm.chains {
		if err := tm.initializeChain(chain); err != nil {
			tm.logger.Error("Failed to initialize chain", "chain", chainName, "error", err)
			return core.WrapError("CHAIN_INIT_FAILED", fmt.Sprintf("failed to initialize chain %s", chainName), err)
		}
	}

	// 启动调度器
	if err := tm.scheduler.Start(ctx); err != nil {
		return core.WrapError("SCHEDULER_START_FAILED", "failed to start scheduler", err)
	}

	// 启动任务
	for taskID, task := range tm.tasks {
		if task.Enabled {
			if err := tm.startTask(ctx, task); err != nil {
				tm.logger.Error("Failed to start task", "task_id", taskID, "error", err)
				continue
			}
		}
	}

	// 启动健康检查
	tm.wg.Add(1)
	go tm.healthCheckWorker()

	tm.running = true
	tm.logger.Info("Task manager started successfully")

	// 发布启动事件
	event := core.Event{
		Type:   "task_manager.started",
		Source: "task_manager",
		Data: map[string]interface{}{
			"task_count":  len(tm.tasks),
			"chain_count": len(tm.chains),
		},
		Timestamp: time.Now(),
	}
	tm.eventBus.Publish(event)

	return nil
}

// StopAll 停止所有任务
func (tm *TaskManager) StopAll() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if !tm.running {
		return nil
	}

	tm.logger.Info("Stopping task manager")

	// 取消上下文
	tm.cancel()

	// 停止调度器
	if err := tm.scheduler.Stop(); err != nil {
		tm.logger.Error("Failed to stop scheduler", "error", err)
	}

	// 停止区块链扫描器
	for chainName, chain := range tm.chains {
		if err := chain.GetScanner().Stop(); err != nil {
			tm.logger.Error("Failed to stop chain scanner", "chain", chainName, "error", err)
		}
	}

	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		tm.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		tm.logger.Info("All tasks stopped gracefully")
	case <-time.After(tm.config.ShutdownTimeout):
		tm.logger.Warn("Shutdown timeout reached, forcing stop")
	}

	tm.running = false

	// 发布停止事件
	event := core.Event{
		Type:      "task_manager.stopped",
		Source:    "task_manager",
		Data:      map[string]interface{}{},
		Timestamp: time.Now(),
	}
	tm.eventBus.Publish(event)

	return nil
}

// GetTaskStatus 获取任务状态
func (tm *TaskManager) GetTaskStatus(taskID string) core.TaskStatus {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	return tm.scheduler.GetTaskStatus(taskID)
}

// GetChainStatus 获取链状态
func (tm *TaskManager) GetChainStatus(chainName string) core.ChainStatus {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	chain, exists := tm.chains[chainName]
	if !exists {
		return core.ChainStatus{
			Name:   chainName,
			Status: "not_found",
		}
	}

	// 获取链的详细状态信息
	latestBlock, _ := chain.GetScanner().GetLatestBlock()

	return core.ChainStatus{
		Name:        chainName,
		Type:        chain.GetChainType(),
		Status:      "active", // 这里可以根据实际情况判断
		LatestBlock: latestBlock,
		LastScan:    time.Now(), // 这里应该从实际的扫描器获取
	}
}

// initializeChain 初始化区块链
func (tm *TaskManager) initializeChain(chain core.ChainProvider) error {
	// 这里可以添加链的初始化逻辑
	// 比如检查连接、验证配置等
	tm.logger.Info("Initializing chain", "chain", chain.GetChainName())
	return nil
}

// startTask 启动单个任务
func (tm *TaskManager) startTask(ctx context.Context, task core.Task) error {
	return tm.scheduler.ScheduleTask(task)
}

// healthCheckWorker 健康检查工作器
func (tm *TaskManager) healthCheckWorker() {
	defer tm.wg.Done()

	ticker := time.NewTicker(tm.config.HealthCheck)
	defer ticker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-ticker.C:
			tm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (tm *TaskManager) performHealthCheck() {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	// 检查任务状态
	for taskID := range tm.tasks {
		status := tm.scheduler.GetTaskStatus(taskID)
		if status.Status == "error" && status.ErrorCount > 5 {
			tm.logger.Warn("Task has too many errors", "task_id", taskID, "error_count", status.ErrorCount)
		}
	}

	// 检查链状态
	for chainName := range tm.chains {
		status := tm.GetChainStatus(chainName)
		if status.Status == "error" {
			tm.logger.Warn("Chain is in error state", "chain", chainName)
		}
	}
}
