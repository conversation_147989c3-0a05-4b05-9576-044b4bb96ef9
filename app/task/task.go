package task

import (
	"context"
	"net/http"
	"sync"
	"time"

	"bepusdt/app/conf"

	"github.com/shopspring/decimal"
)

type task struct {
	duration time.Duration
	callback func(ctx context.Context)
}

var (
	tasks []task
	mu    sync.Mutex
)

var client = &http.Client{Timeout: time.Second * 30}

func Init() error {
	// 按需启用区块链网络
	if conf.IsChainEnabled(conf.Tron) {
		tronInit()
	}
	if conf.IsChainEnabled(conf.Bsc) {
		bscInit()
	}
	if conf.IsChainEnabled(conf.Ethereum) {
		ethInit()
	}
	if conf.IsChainEnabled(conf.Polygon) {
		polygonInit()
	}
	if conf.IsChainEnabled(conf.Arbitrum) {
		arbitrumInit()
	}
	if conf.Is<PERSON>hainEnabled(conf.Xlayer) {
		xlayerInit()
	}
	if conf.IsChainEnabled(conf.Base) {
		baseInit()
	}
	if conf.<PERSON><PERSON>hainEnabled(conf.<PERSON><PERSON>) {
		solanaInit()
	}
	if conf.<PERSON><PERSON>hainEnabled(conf.Aptos) {
		aptosInit()
	}

	return nil
}

func register(t task) {
	mu.Lock()
	defer mu.Unlock()

	if t.callback == nil {

		panic("task callback cannot be nil")
	}

	tasks = append(tasks, t)
}

func inAmountRange(payAmount decimal.Decimal) bool {
	if payAmount.GreaterThan(conf.GetPaymentAmountMax()) {

		return false
	}

	if payAmount.LessThan(conf.GetPaymentAmountMin()) {

		return false
	}

	return true
}
