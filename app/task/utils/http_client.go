package utils

import (
	"bytes"
	"io"
	"net/http"
	"time"

	"bepusdt/app/task/core"
)

// HTTPClient HTTP客户端实现
type HTTPClient struct {
	client  *http.Client
	headers map[string]string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(timeout time.Duration) core.HTTPClient {
	return &HTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
		headers: make(map[string]string),
	}
}

// Get 发送GET请求
func (c *HTTPClient) Get(url string) (*core.HTTPResponse, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, core.WrapError("REQUEST_CREATE_FAILED", "failed to create GET request", err)
	}

	// 设置请求头
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, core.WrapError("REQUEST_FAILED", "GET request failed", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, core.WrapError("RESPONSE_READ_FAILED", "failed to read response body", err)
	}

	// 转换响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	return &core.HTTPResponse{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       body,
	}, nil
}

// Post 发送POST请求
func (c *HTTPClient) Post(url string, data []byte) (*core.HTTPResponse, error) {
	req, err := http.NewRequest("POST", url, bytes.NewReader(data))
	if err != nil {
		return nil, core.WrapError("REQUEST_CREATE_FAILED", "failed to create POST request", err)
	}

	// 设置默认Content-Type
	req.Header.Set("Content-Type", "application/json")

	// 设置自定义请求头
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, core.WrapError("REQUEST_FAILED", "POST request failed", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, core.WrapError("RESPONSE_READ_FAILED", "failed to read response body", err)
	}

	// 转换响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	return &core.HTTPResponse{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       body,
	}, nil
}

// SetTimeout 设置超时时间
func (c *HTTPClient) SetTimeout(timeout time.Duration) {
	c.client.Timeout = timeout
}

// SetHeaders 设置请求头
func (c *HTTPClient) SetHeaders(headers map[string]string) {
	c.headers = headers
}

// AddHeader 添加请求头
func (c *HTTPClient) AddHeader(key, value string) {
	c.headers[key] = value
}

// RemoveHeader 移除请求头
func (c *HTTPClient) RemoveHeader(key string) {
	delete(c.headers, key)
}

// GetHeaders 获取当前请求头
func (c *HTTPClient) GetHeaders() map[string]string {
	result := make(map[string]string)
	for key, value := range c.headers {
		result[key] = value
	}
	return result
}
