package utils

import (
	"sync"
	"time"

	"bepusdt/app/task/core"
)

// Queue 队列实现
type Queue[T any] struct {
	items    []T
	capacity int
	mu       sync.RWMutex
	notEmpty *sync.Cond
	notFull  *sync.Cond
	closed   bool
}

// NewQueue 创建新队列
func NewQueue[T any](capacity int) core.Queue[T] {
	q := &Queue[T]{
		items:    make([]T, 0, capacity),
		capacity: capacity,
		closed:   false,
	}
	q.notEmpty = sync.NewCond(&q.mu)
	q.notFull = sync.NewCond(&q.mu)
	return q
}

// Push 向队列添加元素
func (q *Queue[T]) Push(item T) error {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.closed {
		return core.NewTaskError("QUEUE_CLOSED", "cannot push to closed queue", nil)
	}

	// 等待队列有空间
	for len(q.items) >= q.capacity && !q.closed {
		q.notFull.Wait()
	}

	if q.closed {
		return core.NewTaskError("QUEUE_CLOSED", "queue was closed while waiting", nil)
	}

	q.items = append(q.items, item)
	q.notEmpty.Signal()
	return nil
}

// Pop 从队列取出元素
func (q *Queue[T]) Pop() (T, error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	var zero T

	// 等待队列有元素
	for len(q.items) == 0 && !q.closed {
		q.notEmpty.Wait()
	}

	if q.closed && len(q.items) == 0 {
		return zero, core.NewTaskError("QUEUE_CLOSED", "queue is closed", nil)
	}

	if len(q.items) == 0 {
		return zero, core.NewTaskError("QUEUE_EMPTY", "queue is empty", nil)
	}

	item := q.items[0]
	q.items = q.items[1:]
	q.notFull.Signal()
	return item, nil
}

// TryPush 尝试向队列添加元素（非阻塞）
func (q *Queue[T]) TryPush(item T) error {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.closed {
		return core.NewTaskError("QUEUE_CLOSED", "cannot push to closed queue", nil)
	}

	if len(q.items) >= q.capacity {
		return core.NewTaskError("QUEUE_FULL", "queue is full", nil)
	}

	q.items = append(q.items, item)
	q.notEmpty.Signal()
	return nil
}

// TryPop 尝试从队列取出元素（非阻塞）
func (q *Queue[T]) TryPop() (T, error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	var zero T

	if q.closed && len(q.items) == 0 {
		return zero, core.NewTaskError("QUEUE_CLOSED", "queue is closed", nil)
	}

	if len(q.items) == 0 {
		return zero, core.NewTaskError("QUEUE_EMPTY", "queue is empty", nil)
	}

	item := q.items[0]
	q.items = q.items[1:]
	q.notFull.Signal()
	return item, nil
}

// PushWithTimeout 带超时的Push操作
func (q *Queue[T]) PushWithTimeout(item T, timeout time.Duration) error {
	done := make(chan error, 1)
	
	go func() {
		done <- q.Push(item)
	}()

	select {
	case err := <-done:
		return err
	case <-time.After(timeout):
		return core.NewTaskError("QUEUE_TIMEOUT", "push operation timeout", nil)
	}
}

// PopWithTimeout 带超时的Pop操作
func (q *Queue[T]) PopWithTimeout(timeout time.Duration) (T, error) {
	type result struct {
		item T
		err  error
	}
	
	done := make(chan result, 1)
	
	go func() {
		item, err := q.Pop()
		done <- result{item: item, err: err}
	}()

	select {
	case res := <-done:
		return res.item, res.err
	case <-time.After(timeout):
		var zero T
		return zero, core.NewTaskError("QUEUE_TIMEOUT", "pop operation timeout", nil)
	}
}

// Size 获取队列大小
func (q *Queue[T]) Size() int {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return len(q.items)
}

// IsEmpty 检查队列是否为空
func (q *Queue[T]) IsEmpty() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return len(q.items) == 0
}

// IsFull 检查队列是否已满
func (q *Queue[T]) IsFull() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return len(q.items) >= q.capacity
}

// Capacity 获取队列容量
func (q *Queue[T]) Capacity() int {
	return q.capacity
}

// Close 关闭队列
func (q *Queue[T]) Close() error {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.closed {
		return nil
	}

	q.closed = true
	q.notEmpty.Broadcast()
	q.notFull.Broadcast()
	return nil
}

// IsClosed 检查队列是否已关闭
func (q *Queue[T]) IsClosed() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.closed
}

// Clear 清空队列
func (q *Queue[T]) Clear() {
	q.mu.Lock()
	defer q.mu.Unlock()

	q.items = q.items[:0]
	q.notFull.Broadcast()
}

// Peek 查看队列头部元素（不移除）
func (q *Queue[T]) Peek() (T, error) {
	q.mu.RLock()
	defer q.mu.RUnlock()

	var zero T

	if len(q.items) == 0 {
		return zero, core.NewTaskError("QUEUE_EMPTY", "queue is empty", nil)
	}

	return q.items[0], nil
}

// ToSlice 将队列转换为切片（复制）
func (q *Queue[T]) ToSlice() []T {
	q.mu.RLock()
	defer q.mu.RUnlock()

	result := make([]T, len(q.items))
	copy(result, q.items)
	return result
}
