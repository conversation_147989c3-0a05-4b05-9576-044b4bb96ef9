#!/usr/bin/env bash
set -Eeuo pipefail

# Pretty colors (only if TTY)
if [[ -t 1 ]]; then
  RED="$(tput setaf 1)"; GREEN="$(tput setaf 2)"; YELLOW="$(tput setaf 3)"; BLUE="$(tput setaf 4)";
  MAGENTA="$(tput setaf 5)"; CYAN="$(tput setaf 6)"; BOLD="$(tput bold)"; RESET="$(tput sgr0)"
else
  RED=""; GREEN=""; YELLOW=""; BLUE=""; MAGENTA=""; CYAN=""; BOLD=""; RESET=""
fi

SCRIPT_DIR="$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
MOD_DIR="${SCRIPT_DIR}"

log_info()    { echo -e "${BLUE}${BOLD}›${RESET} $*"; }
log_ok()      { echo -e "${GREEN}${BOLD}✔${RESET} $*"; }
log_warn()    { echo -e "${YELLOW}${BOLD}!${RESET} $*"; }
log_error()   { echo -e "${RED}${BOLD}✖${RESET} $*"; }

banner() {
  echo -e "${MAGENTA}${BOLD}────────────── bepusdt build ──────────────${RESET}"
}

usage() {
  cat <<EOF
${BOLD}用法${RESET}: $(basename "$0") [选项]

选项:
  -o, --os <os|all>         目标系统 (linux|darwin|windows|all). 默认: 当前主机
  -a, --arch <arch|all>     目标架构 (amd64|arm64|all). 默认: 当前主机
  -n, --name <name>         二进制名称. 默认: bepusdt
  -d, --outdir <dir>        输出目录. 默认: dist
  -t, --tags <tags>         额外 build tags (逗号分隔)
  -m, --mode <release|debug>构建模式. 默认: release
      --race                启用竞态检测 (自动启用 CGO)
      --cgo                 启用 CGO (默认关闭)
      --clean               构建前清理输出目录
  -h, --help                显示本帮助

示例:
  $(basename "$0") --os linux --arch arm64 --mode release
  $(basename "$0") -o all -a all -t netgo
EOF
}

parse_version() {
  local f="${MOD_DIR}/app/version.go"
  if [[ -f "$f" ]]; then
    # 提取 const Version = "x.y.z"
    awk -F '"' '/const[[:space:]]+Version[[:space:]]*=/{print $2; exit}' "$f"
  fi
}

hash_file() {
  if command -v shasum >/dev/null 2>&1; then
    shasum -a 256 "$1" | awk '{print $1}'
  elif command -v sha256sum >/dev/null 2>&1; then
    sha256sum "$1" | awk '{print $1}'
  else
    echo "(no-sha256)"
  fi
}

NAME="bepusdt"
OUTDIR="${MOD_DIR}/dist"
MODE="release"
TAGS=""
ENABLE_RACE=0
ENABLE_CGO=0
CLEAN=0

HOST_OS="$(go env GOOS)"
HOST_ARCH="$(go env GOARCH)"

OSES=("$HOST_OS")
ARCHES=("$HOST_ARCH")

while [[ $# -gt 0 ]]; do
  case "$1" in
    -o|--os)
      shift; [[ $# -gt 0 ]] || { log_error "缺少 --os 参数值"; exit 1; }
      if [[ "$1" == "all" ]]; then OSES=(linux darwin windows); else OSES=("$1"); fi
      ;;
    -a|--arch)
      shift; [[ $# -gt 0 ]] || { log_error "缺少 --arch 参数值"; exit 1; }
      if [[ "$1" == "all" ]]; then ARCHES=(amd64 arm64); else ARCHES=("$1"); fi
      ;;
    -n|--name)
      shift; NAME="$1" ;;
    -d|--outdir)
      shift; OUTDIR="$1" ;;
    -t|--tags)
      shift; TAGS="$1" ;;
    -m|--mode)
      shift; MODE="$1" ;;
    --race)
      ENABLE_RACE=1 ;;
    --cgo)
      ENABLE_CGO=1 ;;
    --clean)
      CLEAN=1 ;;
    -h|--help)
      usage; exit 0 ;;
    *)
      log_warn "未知参数: $1"; usage; exit 1 ;;
  esac
  shift || true
done

VERSION="$(parse_version || true)"
[[ -n "$VERSION" ]] || VERSION="0.0.0"

GIT_COMMIT=""
GIT_TAG=""
GIT_DIRTY=""
if command -v git >/dev/null 2>&1 && git -C "$MOD_DIR" rev-parse --is-inside-work-tree >/dev/null 2>&1; then
  GIT_COMMIT="$(git -C "$MOD_DIR" rev-parse --short HEAD 2>/dev/null || true)"
  GIT_TAG="$(git -C "$MOD_DIR" describe --tags --abbrev=0 2>/dev/null || true)"
  git -C "$MOD_DIR" diff --quiet || GIT_DIRTY="-dirty"
fi

banner
log_info "模块    : $(go list -m -f '{{.Path}}' 2>/dev/null || echo bepusdt)"
[[ -n "$GIT_COMMIT" ]] && log_info "Git     : ${GIT_TAG:-n/a} (${GIT_COMMIT}${GIT_DIRTY})"
log_info "模式    : ${MODE}"
log_info "Tags    : ${TAGS:-<none>}"

[[ "$CLEAN" -eq 1 ]] && { log_info "清理输出目录: ${OUTDIR}"; rm -rf -- "$OUTDIR"; }
mkdir -p "$OUTDIR"

LDFLAGS=""
GCFLAGS=""
ASMFLAGS=""

if [[ "$MODE" == "release" ]]; then
  LDFLAGS="-s -w"
else
  GCFLAGS="all=-N -l"
fi

if [[ "$ENABLE_RACE" -eq 1 ]]; then
  ENABLE_CGO=1
fi

build_one() {
  local os="$1" arch="$2"
  local ext=""; [[ "$os" == "windows" ]] && ext=".exe"
  local outdir_os_arch="${OUTDIR}/${os}/${arch}"
  local outbin="${outdir_os_arch}/${NAME}${ext}"

  mkdir -p "$outdir_os_arch"

  local envs=("GOOS=${os}" "GOARCH=${arch}" "CGO_ENABLED=${ENABLE_CGO}")

  local args=("-trimpath")
  [[ -n "$LDFLAGS" ]] && args+=("-ldflags" "$LDFLAGS")
  [[ -n "$GCFLAGS" ]] && args+=("-gcflags" "$GCFLAGS")
  [[ -n "$ASMFLAGS" ]] && args+=("-asmflags" "$ASMFLAGS")
  [[ -n "$TAGS" ]] && args+=("-tags" "$TAGS")
  [[ "$ENABLE_RACE" -eq 1 ]] && args+=("-race")

  log_info "构建 ${os}/${arch} -> ${outbin}"
  (
    cd "$MOD_DIR"
    env ${envs[*]} go build "${args[@]}" -o "$outbin" ./main
  )

  local sz
  if command -v gstat >/dev/null 2>&1; then
    sz="$(gstat -c %s "$outbin")" # GNU stat (brew coreutils)
  else
    sz="$(stat -f %z "$outbin" 2>/dev/null || stat -c %s "$outbin" 2>/dev/null || echo 0)"
  fi
  local human
  if command -v numfmt >/dev/null 2>&1; then
    human="$(numfmt --to=iec --suffix=B "$sz" 2>/dev/null || echo "$sz")"
  else
    human="$sz bytes"
  fi

  local sum
  sum="$(hash_file "$outbin")"
  echo "$sum  $(basename "$outbin")" >"${outbin}.sha256"

  log_ok "完成 ${os}/${arch}  大小: ${human}  sha256: ${sum}"
}

for os in "${OSES[@]}"; do
  for arch in "${ARCHES[@]}"; do
    build_one "$os" "$arch"
  done
done

echo
log_ok "所有目标已构建完成，产物目录: ${OUTDIR}"
echo -e "${CYAN}${BOLD}示例运行${RESET}: ${BOLD}./dist/${HOST_OS}/${HOST_ARCH}/${NAME}${RESET}"


